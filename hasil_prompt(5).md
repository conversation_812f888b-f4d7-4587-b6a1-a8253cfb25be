# Kebutuhan Fungsional: Halaman Pengaturan

Dokumen ini berisi Use Case Description, Activity Diagram, dan Sequence Diagram untuk kebutuhan fungsional: *"Sistem harus menyediakan halaman pengaturan di mana pengguna dapat mengkonfigurasi parameter operasional, seperti ambang batas deteksi (threshold) atau jadwal pembersihan otomatis."*

---

## 1. Use Case Description

### **UC-05: Mengkonfigurasi Pengaturan Sistem**

*   **Actor:** Pen<PERSON><PERSON> (User/Admin)
*   **Description:** Use case ini menjelaskan bagaimana **Pengguna** mengubah parameter operasional sistem, seperti ambang batas kepercayaan (confidence threshold) untuk deteksi spam atau mengaktifkan/menonaktifkan fitur hapus otomatis.
*   **Preconditions:**
    *   Pengguna telah membuka halaman "Settings".
*   **Postconditions:**
    *   **Success:** Parameter operasional sistem berhasil diperbarui dengan nilai yang baru dan akan digunakan untuk proses selanjutnya.
    *   **Failure:** <PERSON>bahan gagal disimpan, dan sistem akan terus beroperasi dengan konfigurasi sebelumnya.

#### **Main Flow (Basic Path)**
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 1 | Membuka halaman "Settings". | |
| 2 | | Menampilkan halaman dengan semua parameter konfigurasi saat ini (misalnya, slider untuk threshold, tombol toggle untuk auto-delete). |
| 3 | Mengubah nilai salah satu parameter (misalnya, menggeser slider threshold dari 0.8 menjadi 0.85). | |
| 4 | Menekan tombol "Simpan Pengaturan". | |
| 5 | | 1. Menerima dan memvalidasi nilai baru. <br> 2. Menyimpan konfigurasi baru ke dalam state aplikasi (dan/atau file konfigurasi). <br> 3. Menampilkan notifikasi "Pengaturan berhasil disimpan." |
| 6 | Melihat notifikasi sukses dan nilai yang ditampilkan di antarmuka telah diperbarui. | |

#### **Alternative Flow (Input Tidak Valid)**
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 3a.1 | Memasukkan nilai yang tidak valid (misalnya, teks pada input angka). | |
| 4a.1 | Menekan tombol "Simpan Pengaturan". | |
| 5a.1 | | Memvalidasi input dan mendeteksi bahwa nilai tidak valid. |
| 5a.2 | | Menampilkan pesan kesalahan, misalnya: "Nilai threshold harus antara 0 dan 1." |
| - | | Use case berakhir, sistem tetap menggunakan pengaturan lama. |

---

## 2. Activity Diagram

Diagram ini menggambarkan alur kerja pengguna saat mengubah sebuah pengaturan.

```plantuml
@startuml
title Activity Diagram: Mengubah Pengaturan Sistem

|Pengguna|
start
:Membuka halaman "Settings";

|Sistem|
:Menampilkan halaman pengaturan
dengan nilai-nilai saat ini;

|Pengguna|
:Mengubah nilai sebuah parameter
(misal: menggeser slider);
:Menekan tombol "Simpan";

|Sistem|
:Menerima dan memvalidasi nilai baru;
if (Nilai valid?) then (Ya)
  :Menyimpan konfigurasi baru;
  :Menampilkan notifikasi sukses;
else (Tidak)
  :Menampilkan pesan error;
endif

|Pengguna|
:Melihat notifikasi;
stop

@enduml
```

---

## 3. Sequence Diagram

Diagram ini menunjukkan interaksi antar komponen saat pengguna mengubah *confidence threshold*.

```plantuml
@startuml
title Sequence Diagram: Mengubah Confidence Threshold

actor Pengguna
participant "Antarmuka Web" as Frontend
participant "Sistem (Backend)" as Backend
participant "Manajemen Konfigurasi" as Config

Pengguna -> Frontend: 1. bukaHalamanSettings()
activate Frontend

Frontend -> Backend: 2. getCurrentConfig()
activate Backend
Backend -> Config: 3. bacaKonfigurasi()
activate Config
Config --> Backend: 4. dataKonfigurasi
deactivate Config
Backend --> Frontend: 5. dataKonfigurasi
deactivate Backend

Frontend -> Pengguna: 6. tampilkanPengaturan(dataKonfigurasi)

Pengguna -> Frontend: 7. ubahThreshold(0.85)
Pengguna -> Frontend: 8. klikSimpan()

Frontend -> Backend: 9. simpanPengaturan({threshold: 0.85})
activate Backend

Backend -> Config: 10. tulisKonfigurasi({threshold: 0.85})
activate Config
Config --> Backend: 11. statusSukses
deactivate Config

Backend --> Frontend: 12. konfirmasiSukses
deactivate Backend

Frontend -> Pengguna: 13. tampilkanNotifikasi("Pengaturan disimpan!")
deactivate Frontend

@enduml
```
