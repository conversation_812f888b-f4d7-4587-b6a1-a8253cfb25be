# Activity Diagram: Mengakses Dashboard (Lokal)

```plantuml
@startuml
title Activity Diagram: Mengakses Dashboard Aplikasi (Lokal)

|Pengguna|
start
:<PERSON><PERSON><PERSON><PERSON> skrip `run.py` (atau start.bat);

|Sistem|
partition "Proses Inisialisasi Aplikasi" {
    :Skrip `run.py` memanggil `scripts/run_streamlit.py`;
    :Streamlit memulai server aplikasi;
    :Memuat halaman utama (dari `src/app/dashboard.py`);
    :Merender komponen UI:
    - Ju<PERSON>l "Judol Remover"
    - Metrik (Comments Processed, Spam Detected, dll.)
    - Status Monitor & Auto Delete
    - Daftar "Recent Posts & Comments";
    :Menampilkan halaman dashboard yang sudah dirender di browser;
}

|Pengguna|
:Melihat halaman dashboard utama dengan semua informasinya;
stop

@enduml
```

---

# Use Case Description: Menjalankan Aplikasi dan Mengakses Dashboard (Lokal)

## 1. Use Case Name
**UC-02: <PERSON><PERSON>lank<PERSON> Aplikasi dan Mengakses Dashboard**

## 2. Actor
**Pengguna** (User)

## 3. Description
Use case ini menjelaskan bagaimana **Pengguna** memulai server aplikasi secara lokal dan melihat halaman dashboard utama di browser.

## 4. Preconditions
- **Pengguna** berada di direktori proyek pada terminal atau command prompt.
- Lingkungan Python dan semua dependensi yang diperlukan (termasuk Streamlit) telah terinstal.

## 5. Postconditions
- **Success Postcondition:** Server aplikasi berjalan di mesin lokal dan halaman dashboard utama terbuka secara otomatis di browser default **Pengguna**.
- **Failure Postcondition:** Pesan kesalahan ditampilkan di terminal jika skrip gagal dijalankan (misalnya, dependensi hilang atau file tidak ditemukan).

## 6. Main Flow (Basic Path)
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 1 | Menjalankan perintah untuk mengeksekusi skrip `run.py` di terminal. | |
| 2 | | 1. Memulai server web Streamlit secara lokal. <br> 2. Mengeksekusi skrip `src/app/dashboard.py` untuk merender konten halaman. <br> 3. Secara otomatis membuka tab browser baru yang mengarah ke URL server lokal (misalnya, `http://localhost:8501`). |
| 3 | Melihat halaman dashboard utama yang dimuat di browser. | |

---

# Sequence Diagram: Menjalankan Aplikasi dan Mengakses Dashboard (Lokal)

```plantuml
@startuml
title Sequence Diagram: Menjalankan Aplikasi Lokal

actor Pengguna
participant "Terminal" as Shell
participant "Skrip Aplikasi (run.py)" as App
participant "Server Streamlit" as Server
participant "Browser" as Browser

Pengguna -> Shell: 1. `python run.py`
activate Shell

Shell -> App: 2. Eksekusi skrip
activate App

App -> Server: 3. Mulai Server()
activate Server
App --> Shell: (Menampilkan log server)
deactivate App
deactivate Shell

Server -> Server: 4. Render Halaman (`dashboard.py`)
Server -> Browser: 5. Buka URL (localhost)
activate Browser

Browser -> Server: 6. HTTP GET Request
Server --> Browser: 7. HTTP Response (Halaman HTML)

Browser --> Pengguna: 8. Tampilkan Halaman Dashboard
deactivate Browser
deactivate Server

@enduml
```
