# Use Case Description: Melakukan Analisis Input Teks

## 1. Use Case Name
**UC-01: Melakukan Analisis Input Teks**

## 2. Actor
**Pengguna** (User)

## 3. Description
Use case ini menjelaskan bagaimana **Pengguna** berinteraksi dengan **Sistem** untuk memasukkan sebuah teks dan mendapatkan hasil klasifikasi apakah teks tersebut adalah "Spam" atau "Bukan Spam".

## 4. Preconditions
- **Pengguna** telah membuka halaman antarmuka web yang menyediakan fitur analisis teks manual.

## 5. Postconditions
- **Success Postcondition:** <PERSON><PERSON> klas<PERSON> ("Spam" atau "Bukan Spam") ditampilkan kepada **Pengguna** di antarmuka.
- **Failure Postcondition:** Sebuah pesan kesalahan ditampilkan kepada **Pengguna** jika input tidak valid atau terjadi kegagalan pada sistem.

## 6. Main Flow (Basic Path)
| Langkah | Aks<PERSON> Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 1 | Memasukkan teks yang ingin dianalisis ke dalam kolom input yang tersedia. | |
| 2 | Menekan tombol "Analisis" (atau yang sejenis). | |
| 3 | | 1. Menerima data teks dari antarmuka. <br> 2. Memvalidasi bahwa input tidak kosong. |
| 4 | | Memanggil model klasifikasi machine learning dengan input teks tersebut. |
| 5 | | Menerima hasil klasifikasi dari model. |
| 6 | | Menampilkan label hasil klasifikasi ("Spam" atau "Bukan Spam") secara jelas di antarmuka. |
| 7 | Melihat hasil klasifikasi yang ditampilkan oleh sistem. | |

## 7. Alternative Flows
### 7a. Input Teks Kosong
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 2a.1 | Menekan tombol "Analisis" tanpa memasukkan teks apa pun. | |
| 3a.1 | | Memvalidasi input dan mendeteksi bahwa input kosong. |
| 3a.2 | | Menampilkan pesan kesalahan kepada **Pengguna**, misalnya: "Input teks tidak boleh kosong." |
| - | | Use case berakhir. |

## 8. Exception Flows
### 8a. Model Klasifikasi Gagal Merespon
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 4b.1 | | Memanggil model klasifikasi, namun model gagal memproses atau mengembalikan hasil (misalnya, karena timeout atau error internal). |
| 4b.2 | | Menampilkan pesan kesalahan kepada **Pengguna**, misalnya: "Terjadi kesalahan saat menganalisis teks. Silakan coba lagi." |
| - | | Use case berakhir. |

---

## 9. Sequence Diagram

```plantuml
@startuml
title Sequence Diagram: Analisis Input Teks

actor Pengguna
participant "Antarmuka Web" as Frontend
participant "Sistem (Backend)" as Backend
participant "Model Klasifikasi" as Model

Pengguna -> Frontend: 1. masukkanTeks(teks)
Pengguna -> Frontend: 2. klikTombolAnalisis()

activate Frontend
Frontend -> Backend: 3. analisisTeks(teks)
activate Backend

Backend -> Backend: 4. validasiInput(teks)

alt Input Valid
    Backend -> Model: 5. klasifikasi(teks)
    activate Model
    Model --> Backend: 6. hasilKlasifikasi
    deactivate Model

    Backend --> Frontend: 7. kirimHasil(hasilKlasifikasi)
    Frontend -> Pengguna: 8. tampilkanHasil(hasilKlasifikasi)
else Input Tidak Valid
    Backend --> Frontend: 7a. kirimError("Input tidak boleh kosong")
    Frontend -> Pengguna: 8a. tampilkanPesanError("Input tidak boleh kosong")
end

deactivate Backend
deactivate Frontend

@enduml
```