

```plantuml
@startuml
' Mengatur tata letak dari kiri ke kanan
left to right direction

' Mendefinisikan aktor utama
actor "<PERSON><PERSON><PERSON>" as User

' Mendefinisikan sistem eksternal
database "Facebook API" as FacebookAPI

' Mendefinisikan batasan sistem dengan use case yang lebih spesifik
rectangle "Sistem Moderasi Spam" {
  usecase "Menyediakan Dashboard Utama" as UC1
  usecase "Monitor Komentar Otomatis" as UC2
  usecase "Pengelolaan Daftar Spam" as UC3
  usecase "Melakukan Analisis Komentar Postingan" as UC4
  usecase "Analisis Teks Manual" as UC_Test
  usecase "Mengubah Pengaturan Sistem" as UC5
  usecase "Melihat Log Aktivitas" as UC6
}

' Pengguna dapat menginisiasi semua fungsionalitas
User -- UC1
User -- UC2
User -- UC3
User -- UC4
User -- UC_Test
User -- UC5
User -- UC6

' Menunjukkan use case mana yang berinteraksi dengan API eksternal
' Koneksi ini telah diperbaiki untuk akurasi
UC1 -- FacebookAPI : (mengambil data postingan)
UC2 -- FacebookAPI : (mengambil & menghapus komentar)
UC3 -- FacebookAPI : (menghapus komentar)
UC4 -- FacebookAPI : (mengambil komentar untuk dianalisis)
UC5 -- FacebookAPI : (memvalidasi kredensial)

' "Menguji Detektor dengan Teks" TIDAK terhubung ke API eksternal.
' Ini adalah interaksi langsung dengan model klasifikasi internal.

@enduml
```
